package main

import (
	"fmt"
)

// IOChannelImpl implements AggregatorToIOChannel
type IOChannelImpl struct {
	readRequestChan  chan interface{}
	writeRequestChan chan WriteRequest
	closeChan        chan bool
	ioModule         *IOModuleImpl
}

// Send sends generic data to IO
func (ch *IOChannelImpl) Send(data interface{}) error {
	select {
	case ch.readRequestChan <- data:
		return nil
	default:
		return fmt.Errorf("IO channel is full")
	}
}

// Receive receives data from IO (not used in this direction)
func (ch *IOChannelImpl) Receive() (interface{}, error) {
	return nil, fmt.Errorf("receive not supported on AggregatorToIOChannel")
}

// Close closes the IO channel
func (ch *IOChannelImpl) Close() error {
	close(ch.readRequestChan)
	close(ch.writeRequest<PERSON>han)
	return nil
}

// SendReadRequest sends read request to firmware
func (ch *IOChannelImpl) SendReadRequest(req interface{}) error {
	select {
	case ch.readRequestChan <- req:
		return nil
	default:
		return fmt.<PERSON><PERSON><PERSON>("IO read request channel is full")
	}
}

// SendWriteRequest sends write request to firmware
func (ch *IOChannelImpl) SendWriteRequest(req WriteRequest) error {
	select {
	case ch.writeRequestChan <- req:
		return nil
	default:
		return fmt.Errorf("IO write request channel is full")
	}
}
