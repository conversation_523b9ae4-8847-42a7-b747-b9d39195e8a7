package main

import (
	"encoding/json"
	"log"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// IOModuleImpl implements the IOModule interface
type IOModuleImpl struct {
	config            Config
	client            mqtt.Client
	clientWrapper     *MQTTClientWrapper
	aggregatorChannel IOToAggregatorChannel
	ioChannel         *IOChannelImpl
	dataTransformer   DataTransformer
	isRunning         bool
}

// NewIOModule creates a new IO module instance
func NewIOModule(dataTransformer DataTransformer) *IOModuleImpl {
	ioChannel := &IOChannelImpl{
		readRequestChan:  make(chan interface{}, 100),
		writeRequestChan: make(chan WriteRequest, 100),
		closeChan:        make(chan bool, 1),
	}

	return &IOModuleImpl{
		ioChannel:       ioChannel,
		dataTransformer: dataTransformer,
		isRunning:       false,
	}
}

// Initialize initializes the IO module with configuration
func (io *IOModuleImpl) Initialize(config Config) error {
	io.config = config

	// Create MQTT client wrapper for firmware communication
	io.clientWrapper = NewMQTTClientWrapper(
		config,
		config.MQTT.ClientID+"-io",
		io.handleFirmwareMessage,
		io.onConnect,
		io.onConnectionLost,
		"IO-Firmware",
	)

	io.client = io.clientWrapper.GetClient()
	io.ioChannel.ioModule = io

	log.Printf("✓ IO Module initialized")
	return nil
}

// Start starts the IO module
func (io *IOModuleImpl) Start() error {
	if io.isRunning {
		return nil
	}

	// Connect to firmware MQTT broker
	if err := io.clientWrapper.Connect(); err != nil {
		return err
	}

	// Start processing requests from aggregator
	go io.processAggregatorRequests()

	io.isRunning = true
	log.Printf("✓ IO Module started")
	return nil
}

// Stop stops the IO module
func (io *IOModuleImpl) Stop() error {
	if !io.isRunning {
		return nil
	}

	io.isRunning = false
	io.clientWrapper.UnsubscribeFromTopics()
	io.clientWrapper.Disconnect()

	// Close channels
	close(io.ioChannel.closeChan)

	log.Printf("✓ IO Module stopped")
	return nil
}

// SetAggregatorChannel sets the channel to communicate with aggregator
func (io *IOModuleImpl) SetAggregatorChannel(channel IOToAggregatorChannel) {
	io.aggregatorChannel = channel
}

// GetIOChannel returns the channel for aggregator to send data to IO
func (io *IOModuleImpl) GetIOChannel() AggregatorToIOChannel {
	return io.ioChannel
}

// handleFirmwareMessage handles messages from firmware
func (io *IOModuleImpl) handleFirmwareMessage(client mqtt.Client, msg mqtt.Message) {
	log.Printf("IO: Received from firmware topic '%s': %s\n", msg.Topic(), string(msg.Payload()))

	if err := io.HandleFirmwareMessage(msg.Topic(), msg.Payload()); err != nil {
		log.Printf("IO: Error handling firmware message: %v", err)
	}
}

// HandleFirmwareMessage processes messages from firmware
func (io *IOModuleImpl) HandleFirmwareMessage(topic string, payload []byte) error {
	// Get topic constants
	topicFirmwareReadDataPeriodicRes := io.getTopicConstant("TopicFirmwareReadDataPeriodicRes")
	topicFirmwareWriteDataRes := io.getTopicConstant("TopicFirmwareWriteDataRes")
	topicFirmwareReadDataRes := io.getTopicConstant("TopicFirmwareReadDataRes")

	switch topic {
	case topicFirmwareReadDataPeriodicRes:
		return io.handlePeriodicData(payload)
	case topicFirmwareWriteDataRes:
		return io.handleWriteResponse(payload)
	case topicFirmwareReadDataRes:
		return io.handleReadResponse(payload)
	default:
		log.Printf("IO: Unknown firmware topic: %s", topic)
	}

	return nil
}

// handlePeriodicData handles periodic data from firmware
func (io *IOModuleImpl) handlePeriodicData(payload []byte) error {
	var periodicDataIO PeriodicDataIO
	if err := json.Unmarshal(payload, &periodicDataIO); err != nil {
		return err
	}

	// Transform to cloud format
	cloudData := io.dataTransformer.TransformPeriodicDataToCloud(periodicDataIO)

	// Send to aggregator
	if io.aggregatorChannel != nil {
		return io.aggregatorChannel.SendPeriodicData(cloudData)
	}

	return nil
}

// handleWriteResponse handles write response from firmware
func (io *IOModuleImpl) handleWriteResponse(payload []byte) error {
	var periodicDataIO PeriodicDataIO
	if err := json.Unmarshal(payload, &periodicDataIO); err != nil {
		return err
	}

	// Transform to cloud format
	cloudData := io.dataTransformer.TransformPeriodicDataToCloud(periodicDataIO)

	// Send to aggregator
	if io.aggregatorChannel != nil {
		return io.aggregatorChannel.SendWriteResponse(cloudData)
	}

	return nil
}

// handleReadResponse handles read response from firmware
func (io *IOModuleImpl) handleReadResponse(payload []byte) error {
	var periodicDataIO PeriodicDataIO
	if err := json.Unmarshal(payload, &periodicDataIO); err != nil {
		return err
	}

	// Transform to cloud format
	cloudData := io.dataTransformer.TransformPeriodicDataToCloud(periodicDataIO)

	// Send to aggregator
	if io.aggregatorChannel != nil {
		return io.aggregatorChannel.SendReadResponse(cloudData)
	}

	return nil
}

// SendToFirmware sends data to firmware
func (io *IOModuleImpl) SendToFirmware(topic string, data interface{}) error {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return err
	}

	if token := io.client.Publish(topic, GetQoS(), false, jsonData); token.Wait() && token.Error() != nil {
		return token.Error()
	}

	log.Printf("IO: ✓ Sent to firmware topic %s", topic)
	return nil
}

// processAggregatorRequests processes requests from aggregator
func (io *IOModuleImpl) processAggregatorRequests() {
	for io.isRunning {
		select {
		case req := <-io.ioChannel.readRequestChan:
			io.handleReadRequest(req)
		case req := <-io.ioChannel.writeRequestChan:
			io.handleWriteRequest(req)
		case <-io.ioChannel.closeChan:
			return
		}
	}
}

// handleReadRequest handles read requests from aggregator
func (io *IOModuleImpl) handleReadRequest(req interface{}) {
	topic := io.getTopicConstant("TopicFirmwareReadDataReq")
	if err := io.SendToFirmware(topic, req); err != nil {
		log.Printf("IO: Error sending read request to firmware: %v", err)
	}
}

// handleWriteRequest handles write requests from aggregator
func (io *IOModuleImpl) handleWriteRequest(req WriteRequest) {
	topic := io.getTopicConstant("TopicFirmwareWriteDataReq")
	if err := io.SendToFirmware(topic, req); err != nil {
		log.Printf("IO: Error sending write request to firmware: %v", err)
	}
}

// getTopicConstant gets topic constant from config
func (io *IOModuleImpl) getTopicConstant(topicName string) string {
	// Check subscribe topics first
	if topic, ok := io.config.SubscribeTopics[topicName]; ok {
		return topic
	}
	// Check publish topics
	if topic, ok := io.config.PublishTopics[topicName]; ok {
		return topic
	}
	return ""
}

// onConnect handles connection events
func (io *IOModuleImpl) onConnect(client mqtt.Client) {
	log.Printf("IO: Connected to firmware MQTT broker")
	io.clientWrapper.SubscribeToTopics()
}

// onConnectionLost handles connection lost events
func (io *IOModuleImpl) onConnectionLost(client mqtt.Client, err error) {
	log.Printf("IO: Connection to firmware lost: %v", err)
}
