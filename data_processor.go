package main

import (
	"encoding/json"
	"log"
	"strings"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// DataProcessor handles common data processing operations
type DataProcessor struct{}

// NewDataProcessor creates a new data processor
func NewDataProcessor() *DataProcessor {
	return &DataProcessor{}
}

// ProcessAndPublishPeriodicData is a common function to handle periodic data processing
// It parses PeriodicDataIO, transforms to cloud format, and publishes to specified topic
func (dp *DataProcessor) ProcessAndPublishPeriodicData(payload []byte, client mqtt.Client, topic string) error {
	// Parse the incoming data
	var periodicDataIO PeriodicDataIO
	if err := json.Unmarshal(payload, &periodicDataIO); err != nil {
		log.Printf("Error parsing periodic data: %v\n", err)
		return err
	}

	// Transform the data using the helper function
	cloudPeriodicData := dp.TransformPeriodicDataToCloud(periodicDataIO)

	// Publish the transformed data
	dp.PublishJSON(client, topic, cloudPeriodicData)
	return nil
}

// TransformPeriodicDataToCloud transforms periodic data IO to cloud format with zone mapping
func (dp *DataProcessor) TransformPeriodicDataToCloud(periodicDataIO PeriodicDataIO) CloudPeriodicData {
	// Create a map to group cards by zone
	zoneCards := make(map[string][]PeriodicCard)

	// Group cards by their zones using the mapping
	cardsToZonesMapping := GetCardsToZonesMapping()
	for _, card := range periodicDataIO.Cards {
		zoneID := periodicDataIO.ZoneID // Default to zone ID from payload
		if mappedZoneID, exists := cardsToZonesMapping[card.CardID]; exists {
			zoneID = mappedZoneID
		} else {
			log.Printf("No zone mapping found for card %s, using zone ID from payload: %s", card.CardID, zoneID)
		}
		// Set CardName equal to CardID
		card.CardName = card.CardID
		zoneCards[zoneID] = append(zoneCards[zoneID], card)
	}

	// Create the cloud periodic data structure
	cloudPeriodicData := CloudPeriodicData{
		Timestamp: periodicDataIO.Timestamp,
		Zones:     make([]CloudPeriodicZone, 0),
	}

	// Convert the map to the required format
	zoneIdToNamesMapping := GetZoneIdToNamesMapping()
	zoneIdToHubsMapping := GetZoneIdToHubsMapping()
	for zoneID, cards := range zoneCards {
		cloudZone := CloudPeriodicZone{
			HubID:    zoneIdToHubsMapping[zoneID],
			ZoneID:   zoneID,
			ZoneName: zoneIdToNamesMapping[zoneID],
			Cards:    cards,
		}
		cloudPeriodicData.Zones = append(cloudPeriodicData.Zones, cloudZone)
	}

	return cloudPeriodicData
}

// PublishJSON publishes JSON data to a topic
func (dp *DataProcessor) PublishJSON(client mqtt.Client, topic string, data interface{}) {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		log.Printf("Error marshaling data: %v\n", err)
		return
	}

	if token := client.Publish(topic, GetQoS(), false, jsonData); token.Wait() && token.Error() != nil {
		log.Printf("Failed to publish to %s: %v\n", topic, token.Error())
	} else {
		log.Printf("✓ Published to %s:\n%v\n", topic, string(jsonData))
	}
}

// FilterPeriodicData filters periodic data based on request type
func (dp *DataProcessor) FilterPeriodicData(data CloudPeriodicData, req ReadRequest) CloudPeriodicData {
	filteredData := CloudPeriodicData{
		Timestamp: data.Timestamp,
		Zones:     make([]CloudPeriodicZone, 0),
	}

	zoneIdToNamesMapping := GetZoneIdToNamesMapping()
	zoneIdToHubsMapping := GetZoneIdToHubsMapping()

	switch req.RequestType {
	case "all":
		return data
	case "zone":
		return dp.filterByZone(data, req.Value, filteredData)
	case "card":
		return dp.filterByCard(data, req.Value, filteredData, zoneIdToNamesMapping, zoneIdToHubsMapping)
	case "channel":
		return dp.filterByChannel(data, req.Value, filteredData, zoneIdToNamesMapping, zoneIdToHubsMapping)
	}

	return filteredData
}

// filterByZone filters data by zone ID
func (dp *DataProcessor) filterByZone(data CloudPeriodicData, value string, filteredData CloudPeriodicData) CloudPeriodicData {
	zoneId := strings.TrimSpace(value)
	if strings.Contains(zoneId, "/") {
		parts := strings.Split(zoneId, "/")
		if len(parts) > 1 {
			zoneId = strings.TrimSpace(parts[1])
		}
	}
	zoneId = strings.Trim(zoneId, "${}")

	for _, zone := range data.Zones {
		if zone.ZoneID == zoneId {
			filteredData.Zones = append(filteredData.Zones, zone)
			break
		}
	}
	return filteredData
}

// filterByCard filters data by card ID
func (dp *DataProcessor) filterByCard(data CloudPeriodicData, value string, filteredData CloudPeriodicData, zoneIdToNamesMapping, zoneIdToHubsMapping map[string]string) CloudPeriodicData {
	parts := strings.Split(value, "/")
	if len(parts) != 2 {
		return filteredData
	}
	zoneId := parts[0]
	cardId := parts[1]

	for _, zone := range data.Zones {
		if zone.ZoneID == zoneId {
			filteredZone := CloudPeriodicZone{
				HubID:    zoneIdToHubsMapping[zone.ZoneID],
				ZoneID:   zone.ZoneID,
				ZoneName: zoneIdToNamesMapping[zone.ZoneID],
				Cards:    make([]PeriodicCard, 0),
			}
			for _, card := range zone.Cards {
				if card.CardID == cardId {
					filteredZone.Cards = append(filteredZone.Cards, card)
					break
				}
			}
			if len(filteredZone.Cards) > 0 {
				filteredData.Zones = append(filteredData.Zones, filteredZone)
			}
			break
		}
	}
	return filteredData
}

// filterByChannel filters data by channel ID
func (dp *DataProcessor) filterByChannel(data CloudPeriodicData, value string, filteredData CloudPeriodicData, zoneIdToNamesMapping, zoneIdToHubsMapping map[string]string) CloudPeriodicData {
	parts := strings.Split(value, "/")
	if len(parts) != 3 {
		return filteredData
	}
	zoneId := parts[0]
	cardId := parts[1]
	channelId := parts[2]

	for _, zone := range data.Zones {
		if zone.ZoneID == zoneId {
			filteredZone := CloudPeriodicZone{
				HubID:    zoneIdToHubsMapping[zone.ZoneID],
				ZoneID:   zone.ZoneID,
				ZoneName: zoneIdToNamesMapping[zone.ZoneID],
				Cards:    make([]PeriodicCard, 0),
			}
			for _, card := range zone.Cards {
				if card.CardID == cardId {
					filteredCard := PeriodicCard{
						CardID:   card.CardID,
						Channels: make([]PeriodicChannel, 0),
					}
					for _, channel := range card.Channels {
						if channel.ChannelID == channelId {
							filteredCard.Channels = append(filteredCard.Channels, channel)
							break
						}
					}
					if len(filteredCard.Channels) > 0 {
						filteredZone.Cards = append(filteredZone.Cards, filteredCard)
					}
					break
				}
			}
			if len(filteredZone.Cards) > 0 {
				filteredData.Zones = append(filteredData.Zones, filteredZone)
			}
			break
		}
	}
	return filteredData
}

// TransformConfigToCloud transforms IO firmware config to cloud platform format
func (dp *DataProcessor) TransformConfigToCloud(ioConfig *IOFirmwareConfig) *CloudPlatformConfig {
	return dp.TransformToCloudPlatformConfig(ioConfig)
}

// TransformToCloudPlatformConfig transforms IO firmware config to cloud platform format
func (dp *DataProcessor) TransformToCloudPlatformConfig(ioConfig *IOFirmwareConfig) *CloudPlatformConfig {
	cloudConfig := &CloudPlatformConfig{
		UpdatedAt: time.Now().UnixMilli(),
		Zones:     []CloudZone{},
	}

	// Create a map to group cards by their zones
	zoneCards := make(map[string][]CloudCard)

	// Get mappings from config
	cardsToZonesMapping := GetCardsToZonesMapping()
	zoneIdToNamesMapping := GetZoneIdToNamesMapping()
	zoneIdToHubsMapping := GetZoneIdToHubsMapping()

	// Process all cards and group them by their mapped zones
	for _, port := range ioConfig.Ports {
		for _, card := range port.Cards {
			cloudCard := CloudCard{
				CardID:   card.CardID,
				CardName: card.CardID,
				Channels: []CloudChannel{},
			}

			for _, channel := range card.Channels {
				cloudChannel := CloudChannel{
					ChannelID:   channel.ChannelID,
					ChannelName: channel.ChannelName,
					Control:     nil,
				}

				// Transform type: input -> read, output -> write
				switch channel.Type {
				case "input":
					cloudChannel.Type = "read"
				case "output":
					cloudChannel.Type = "write"
				}

				// Transform signal type: remove specific voltage/current suffixes for analog
				if strings.HasPrefix(channel.SignalType, "digital") {
					cloudChannel.SignalType = "digital"
					cloudChannel.Range = nil
				} else if strings.HasPrefix(channel.SignalType, "analog") {
					cloudChannel.SignalType = "analog"
					cloudChannel.Range = channel.Range
				}

				cloudCard.Channels = append(cloudCard.Channels, cloudChannel)
			}

			// Determine the zone for this card using the mapping
			zoneID := ioConfig.ZoneName // Default to zone from config
			if mappedZoneID, exists := cardsToZonesMapping[card.CardID]; exists {
				zoneID = mappedZoneID
			} else {
				log.Printf("No zone mapping found for card %s, using zone ID from config: %s", card.CardID, zoneID)
			}

			// Add the card to the appropriate zone
			zoneCards[zoneID] = append(zoneCards[zoneID], cloudCard)
		}
	}

	// Convert the map to the required format
	for zoneID, cards := range zoneCards {
		cloudZone := CloudZone{
			HubID:    zoneIdToHubsMapping[zoneID],
			ZoneID:   zoneID,
			ZoneName: zoneIdToNamesMapping[zoneID],
			Cards:    cards,
		}
		cloudConfig.Zones = append(cloudConfig.Zones, cloudZone)
	}

	return cloudConfig
}
