# Argus Cloud Connector

### todos

-   [x] zone mapping from client config
-   [x] hub mapping from server config
-   [x] single config for mapping
-   [x] reverse zone to card mapping
-   [x] modularize main code
-   [ ] write
-   [ ] update
-   [ ] scale
    -   i can apply linear scale with linear interpolation
    -   should i do it for channels?
    -   how will i know to whome to apply scaling?
-   [ ] zone name or zone id should be unique
-   [ ] separate aggregatorClient and io
    -   create a new folder for the aggregatorClient and io and move respective code there
    -   io will be responsible for getting data from the client(firmware) and sending it to the aggregatorClient
    -   io will be also responsible for getting data from aggregatorClient and sending it to the firmware
    -   aggregatorClient will be responsible for getting data from the io and sending it to the cloud
    -   aggregatorClient will be also responsible for getting data from the cloud and sending it to the io
