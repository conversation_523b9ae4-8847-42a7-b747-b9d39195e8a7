package main

import (
	"fmt"
	"log"
)

// AggregatorChannelImpl implements IOToAggregatorChannel
type AggregatorChannelImpl struct {
	periodicDataChan  chan CloudPeriodicData
	writeResponseChan chan CloudPeriodicData
	readResponseChan  chan CloudPeriodicData
	closeChan         chan bool
	aggregatorClient  *AggregatorClientImpl
}

// Send sends generic data to aggregator
func (ch *AggregatorChannelImpl) Send(data interface{}) error {
	if cloudData, ok := data.(CloudPeriodicData); ok {
		return ch.SendPeriodicData(cloudData)
	}
	return fmt.Errorf("unsupported data type for aggregator channel")
}

// Receive receives data from aggregator (not used in this direction)
func (ch *AggregatorChannelImpl) Receive() (interface{}, error) {
	return nil, fmt.Errorf("receive not supported on IOToAggregatorChannel")
}

// Close closes the aggregator channel
func (ch *AggregatorChannelImpl) Close() error {
	select {
	case ch.close<PERSON>han <- true:
	default:
	}
	return nil
}

// SendPeriodicData sends periodic data to aggregator
func (ch *AggregatorChannelImpl) SendPeriodicData(data CloudPeriodicData) error {
	select {
	case ch.periodicDataChan <- data:
		return nil
	default:
		return fmt.Errorf("aggregator periodic data channel is full")
	}
}

// SendWriteResponse sends write response to aggregator
func (ch *AggregatorChannelImpl) SendWriteResponse(data CloudPeriodicData) error {
	select {
	case ch.writeResponseChan <- data:
		return nil
	default:
		return fmt.Errorf("aggregator write response channel is full")
	}
}

// SendReadResponse sends read response to aggregator
func (ch *AggregatorChannelImpl) SendReadResponse(data CloudPeriodicData) error {
	select {
	case ch.readResponseChan <- data:
		return nil
	default:
		return fmt.Errorf("aggregator read response channel is full")
	}
}

// StartProcessing starts processing messages for the aggregator
func (ch *AggregatorChannelImpl) StartProcessing() {
	go func() {
		log.Printf("Aggregator: Started processing channel messages")
		for {
			select {
			case data := <-ch.periodicDataChan:
				ch.handlePeriodicData(data)
			case data := <-ch.writeResponseChan:
				ch.handleWriteResponse(data)
			case data := <-ch.readResponseChan:
				ch.handleReadResponse(data)
			case <-ch.closeChan:
				log.Printf("Aggregator: Stopped processing channel messages")
				return
			}
		}
	}()
}

// handlePeriodicData handles periodic data for aggregator
func (ch *AggregatorChannelImpl) handlePeriodicData(data CloudPeriodicData) {
	if ch.aggregatorClient == nil {
		log.Printf("Aggregator: aggregatorClient is nil, cannot handle periodic data")
		return
	}

	// Store the data for recent data requests
	ch.aggregatorClient.StorePeriodicData(data)

	// Send to cloud
	topic := ch.aggregatorClient.getTopicConstant("TopicArgusPeriodic")
	if err := ch.aggregatorClient.SendToCloud(topic, data); err != nil {
		log.Printf("Aggregator: Error sending periodic data to cloud: %v", err)
	}
}

// handleWriteResponse handles write response for aggregator
func (ch *AggregatorChannelImpl) handleWriteResponse(data CloudPeriodicData) {
	if ch.aggregatorClient == nil {
		log.Printf("Aggregator: aggregatorClient is nil, cannot handle write response")
		return
	}

	// Send to cloud
	topic := ch.aggregatorClient.getTopicConstant("TopicArgusResponseControlData")
	if err := ch.aggregatorClient.SendToCloud(topic, data); err != nil {
		log.Printf("Aggregator: Error sending write response to cloud: %v", err)
	}
}

// handleReadResponse handles read response for aggregator
func (ch *AggregatorChannelImpl) handleReadResponse(data CloudPeriodicData) {
	if ch.aggregatorClient == nil {
		log.Printf("Aggregator: aggregatorClient is nil, cannot handle read response")
		return
	}

	// Send to cloud
	topic := ch.aggregatorClient.getTopicConstant("TopicArgusPeriodic")
	if err := ch.aggregatorClient.SendToCloud(topic, data); err != nil {
		log.Printf("Aggregator: Error sending read response to cloud: %v", err)
	}
}
