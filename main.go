package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/joho/godotenv"

	"argus-cloud-connector/aggregatorClient"
	"argus-cloud-connector/io"
	"argus-cloud-connector/shared"
)

const version = "0.0.11"

// Global variables for the new modular structure
var messageBridge *shared.MessageBridgeImpl
var configManager *shared.ConfigManager
var dataProcessor *shared.DataProcessor
var validator *shared.Validator

// Helper function for graceful shutdown
func shutdown() {
	log.Println("\nShutting down...")
	if messageBridge != nil {
		messageBridge.Stop()
	}
	log.Println("Disconnected from MQTT brokers")
}

func main() {
	log.SetFlags(0)

	log.Print("Starting Argus Cloud Connector... v", version)

	_ = godotenv.Load()

	// Initialize configuration manager
	configManager = shared.NewConfigManager()

	// Load configuration values
	configManager.LoadConfigValues()

	if shared.GetLogTimestamp() {
		log.SetFlags(log.LstdFlags)
	}

	// Load configurations
	if err := configManager.LoadConfigs(); err != nil {
		log.Fatalf("Error loading MQTT configs: %v", err)
	}

	configManager.PrintConfigs()

	// Initialize shared services
	dataProcessor = shared.NewDataProcessor()
	validator = shared.NewValidator()

	// Create IO module
	ioModule := io.NewIOModule(dataProcessor)
	if err := ioModule.Initialize(shared.ClientConfig); err != nil {
		log.Fatalf("Failed to initialize IO module: %v", err)
	}

	// Create Aggregator Client module
	aggregatorModule := aggregatorClient.NewAggregatorClient(dataProcessor, validator, configManager)
	if err := aggregatorModule.Initialize(shared.ServerConfig); err != nil {
		log.Fatalf("Failed to initialize Aggregator Client module: %v", err)
	}

	// Create and configure message bridge
	messageBridge = shared.NewMessageBridge()
	if err := messageBridge.ConnectModules(ioModule, aggregatorModule); err != nil {
		log.Fatalf("Failed to connect modules: %v", err)
	}

	// Start the message bridge (this starts both modules)
	if err := messageBridge.Start(); err != nil {
		log.Fatalf("Failed to start message bridge: %v", err)
	}

	// !NOTE: Wait for connections to stabilize
	time.Sleep(1 * time.Second)

	// Set up graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	log.Printf("\n✓ Argus Cloud Connector is running with modular architecture")
	log.Printf("  - IO Module: Handles firmware communication")
	log.Printf("  - Aggregator Client: Handles cloud communication")
	log.Printf("  - Message Bridge: Facilitates data flow between modules")
	log.Println("Press Ctrl+C to exit...")

	// Wait for interrupt signal
	<-c
	shutdown()
}
