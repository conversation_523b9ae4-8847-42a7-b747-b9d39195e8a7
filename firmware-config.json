{"zoneName": "zoneA", "deviceId": "1234567890", "mqttConf": {"host": "127.0.0.1", "port": 1883, "username": "admin", "password": "admin"}, "mqttTopics": {"readReqTopic": "readData/req", "readResTopic": "readData/res", "writeReqTopic": "writeData/req", "writeResTopic": "writeData/res", "periodicResTopic": "readData/periodic/res", "periodicityInterval": 5000}, "retry": {"count": 3, "waitTime": 5000}, "ports": [{"baudRate": 115200, "parity": "NONE", "cards": [{"cardId": "CD1", "modbusAddr": "0x02", "channels": [{"channelId": "CH1", "channelName": "East Roof Vent 1 Open", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "East Roof Vent 1 Close", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "East Roof Vent 2 Open", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}]}, {"cardId": "CD2", "modbusAddr": "0x03", "channels": [{"channelId": "CH1", "channelName": "East Roof Vent 2 Close", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "West Roof Vent 1 Open", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "West Roof Vent 1 Close", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH4", "channelName": "Climate Humidity", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH5", "channelName": "Climate Temperature", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH6", "channelName": "Backup Temperature", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}]}, {"cardId": "CD3", "modbusAddr": "0x04", "channels": [{"channelId": "CH1", "channelName": "West Roof Vent 2 Open", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "West Roof Vent 2 Close", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "Horizontal Shade 1 Open", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}]}, {"cardId": "CD4", "modbusAddr": "0x04", "channels": [{"channelId": "CH1", "channelName": "Horizontal Shade 1 Close", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "Horizontal Shade 2 Open", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "Horizontal Shade 2 Close", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}]}, {"cardId": "CD5", "modbusAddr": "0x04", "channels": [{"channelId": "CH1", "channelName": "Unit Heater Enable 1", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "Unit Heater Enable 2", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "HAF Enable", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}]}, {"cardId": "CD6", "modbusAddr": "0x04", "channels": [{"channelId": "CH1", "channelName": "HAF Enable", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "East Roof Vent 1 Open", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "East Roof Vent 1 Close", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}]}, {"cardId": "CD7", "modbusAddr": "0x04", "channels": [{"channelId": "CH1", "channelName": "West Roof Vent 1 Open", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "West Roof Vent 1 Close", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "Horizontal Shade 1 Open", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH4", "channelName": "Climate Humidity", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH5", "channelName": "Climate Temperature", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH6", "channelName": "Backup Temperature", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}]}, {"cardId": "CD8", "modbusAddr": "0x04", "channels": [{"channelId": "CH1", "channelName": "Horizontal Shade 1 Close", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "Unit Heater Enable 1", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "Unit Heater Enable 2", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}]}, {"cardId": "CD9", "modbusAddr": "0x04", "channels": [{"channelId": "CH1", "channelName": "East Roof Vent 1 Open", "type": "output", "signalType": "digital", "value": {"address": "0x00", "bytes": 4}}, {"channelId": "CH2", "channelName": "East Roof Vent 1 Close", "type": "output", "signalType": "digital", "value": {"address": "0x01", "bytes": 4}}, {"channelId": "CH3", "channelName": "West Roof Vent 1 Open", "type": "output", "signalType": "digital", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH4", "channelName": "Climate Humidity", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH5", "channelName": "Climate Temperature", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH6", "channelName": "Backup Temperature", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH7", "channelName": "Mix Valve Supply Pipe Temp", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}, {"channelId": "CH8", "channelName": "Mix Valve Return Pipe Temp", "type": "input", "signalType": "analog", "value": {"address": "0x02", "bytes": 4}}]}]}]}