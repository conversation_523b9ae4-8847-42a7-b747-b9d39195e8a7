# Argus cloud communication messages

This document describes the MQTT topic and message format for the server(cloud platform). The configuration for server topics is defined in `config-server.json`.

## Message Types and Flows

### Message 1: Sensor Message (Periodic)

-   MQTT Topic: `${customer}/${site}/${building}/periodic`
-   EX: `"argus/bangalore/aatmunnOffice/periodic"`
-   Type: Subscribe only topic
-   receive periodic data from IO Firmware (default 5 seconds, can be configured)
-   QoS: 1

```json
// response payload
{
    "timestamp": 1716123200000,
    "zones": [
        {
            "hubId": "hub1",
            "zoneId": "zone_a",
            "zoneName": "Zone A",
            "cards": [
                {
                    "cardId": "card1",
                    "cardName": "Card 1",
                    "channels": [
                        {
                            "channelId": "CH1",
                            "channelName": "Unit Heater",
                            "value": 1,
                            "unit": "C",
                            "status": "success"
                        },
                        {
                            "channelId": "CH5",
                            "channelName": "Humidity",
                            "value": 5,
                            "unit": "%",
                            "status": "success"
                        }
                    ]
                }
            ]
        }
    ]
}
```

---

### Message 2: On-Demand Sensor Pull

-   MQTT Topic: `${customer}/${site}/${building}/pullSensorData`
-   EX: `"argus/bangalore/aatmunnOffice/pullSensorData"`
-   Type: Publish only topic
-   QoS: 1
-   pull on demand data from IO Firmware

```json
// request payload
{
    "requestType": "all / zone / card / channel",
    "value": "* / ${zoneId} / ${cardId} / ${channel}"
}
```

Examples:

1. Get all data:

```json
{
    "requestType": "all",
    "value": "*"
}
```

2. Get zone data:

```json
{
    "requestType": "zone",
    "value": "zone_a"
}
```

3. Get card data:

```json
{
    "requestType": "card",
    "value": "zone_a/CD1"
}
```

4. Get channel data:

```json
{
    "requestType": "channel",
    "value": "zone_a/CD1/CH1"
}
```

-   Response Topic: `${customer}/${site}/${building}/periodic`
-   EX: `"argus/bangalore/aatmunnOffice/periodic"`
-   Type: Subscribe only topic
-   QoS: 1
-   Response data: Same as Message 1

---

### Message 3: Control messages

-   MQTT req topic: `${customer}/${site}/${building}/reqControlData`
-   EX: `"argus/bangalore/aatmunnOffice/reqControlData"`
-   Type: Publish only topic
-   QoS: 1
-   send control message to IO Firmware

```json
// request payload
{
    "hubId": "hub1",
    "zoneId": "zone_a",
    "cardId": "card1",
    "channelId": "CH1",
    "channelName": "Unit Heater", // Required field
    "value": 0
}
```

-   MQTT resp topic: `${customer}/${site}/${building}/responseControlData`
-   EX: `"argus/bangalore/aatmunnOffice/responseControlData"`
-   Type: Subscribe only topic
-   QoS: 1
-   receive control response from IO Firmware

```json
// response payload
{
    "timestamp": 1749548733000,
    "zones": [
        {
            "hubId": "hub1",
            "zoneId": "zone_a",
            "zoneName": "Zone A",
            "cards": [
                {
                    "cardId": "CD1",
                    "cardName": "Card 1",
                    "channels": [
                        {
                            "channelId": "CH1",
                            "channelName": "Unit Heater",
                            "value": 0,
                            "unit": "C",
                            "status": "success"
                        }
                    ]
                }
            ]
        }
    ]
}
```

---

### Message 4: Configuration Message

-   MQTT topic: `${customer}/${site}/${building}/config`
-   EX: `"argus/bangalore/aatmunnOffice/config"`
-   Type: Subscribe only topic
-   QoS: 1
-   control is optional field whose value can be `auto/manual/null`
-   receive configuration from IO Firmware

```json
// response payload
{
    "updatedAt": 1748518927430,
    "zones": [
        {
            "hubId": "hub1",
            "zoneId": "zone_a",
            "zoneName": "Zone A",
            "cards": [
                {
                    "cardId": "CD1",
                    "channels": [
                        {
                            "channelId": "CH1",
                            "channelName": "Unit Heater",
                            "type": "write",
                            "signalType": "digital",
                            "control": null
                        },
                        {
                            "channelId": "CH2",
                            "channelName": "Vent Fan",
                            "type": "write",
                            "signalType": "digital",
                            "control": null
                        },
                        {
                            "channelId": "CH4",
                            "channelName": "Temperature",
                            "type": "read",
                            "signalType": "analog"
                            "control": null
                        },
                        {
                            "channelId": "CH5",
                            "channelName": "Humidity",
                            "type": "read",
                            "signalType": "analog"
                            "control": null
                        }
                    ]
                }
            ]
        }
    ]
}
```

### Message 5: Get Configuration

-   MQTT topic: `${customer}/${site}/${building}/getConfig`
-   EX: `"argus/bangalore/aatmunnOffice/getConfig"`
-   Type: Publish only topic
-   QoS: 1
-   Send request to get configuration from IO Firmware

```json
// request payload
{
    "requestType": "all / zone",
    "value": "* / ${zoneId}"
}
```
