package shared

import (
	"log"
)

// MessageBridgeImpl implements the MessageBridge interface
type MessageBridgeImpl struct {
	ioModule         IOModule
	aggregatorModule AggregatorClientModule
	ioToAggregator   IOToAggregatorChannel
	aggregatorToIO   AggregatorToIOChannel
	isRunning        bool
}

// NewMessageBridge creates a new message bridge
func NewMessageBridge() *MessageBridgeImpl {
	return &MessageBridgeImpl{
		isRunning: false,
	}
}

// ConnectModules connects IO and AggregatorClient modules
func (mb *MessageBridgeImpl) ConnectModules(ioMod IOModule, aggregatorMod AggregatorClientModule) error {
	mb.ioModule = ioMod
	mb.aggregatorModule = aggregatorMod

	// Get channels from modules
	mb.ioToAggregator = aggregatorMod.GetAggregatorChannel()
	mb.aggregatorToIO = ioMod.GetIOChannel()

	// Set up bidirectional communication
	ioMod.SetAggregatorChannel(mb.ioToAggregator)
	aggregatorMod.SetIOChannel(mb.aggregatorToIO)

	log.Printf("✓ Message Bridge: Connected IO and Aggregator modules")
	return nil
}

// Start starts the message bridge
func (mb *MessageBridgeImpl) Start() error {
	if mb.isRunning {
		return nil
	}

	// Start both modules
	if err := mb.ioModule.Start(); err != nil {
		return err
	}

	if err := mb.aggregatorModule.Start(); err != nil {
		return err
	}

	mb.isRunning = true
	log.Printf("✓ Message Bridge: Started")
	return nil
}

// Stop stops the message bridge
func (mb *MessageBridgeImpl) Stop() error {
	if !mb.isRunning {
		return nil
	}

	mb.isRunning = false

	// Stop both modules
	if err := mb.ioModule.Stop(); err != nil {
		log.Printf("Error stopping IO module: %v", err)
	}

	if err := mb.aggregatorModule.Stop(); err != nil {
		log.Printf("Error stopping Aggregator module: %v", err)
	}

	// Close channels
	if mb.ioToAggregator != nil {
		mb.ioToAggregator.Close()
	}
	if mb.aggregatorToIO != nil {
		mb.aggregatorToIO.Close()
	}

	log.Printf("✓ Message Bridge: Stopped")
	return nil
}

// GetIOModule returns the IO module
func (mb *MessageBridgeImpl) GetIOModule() IOModule {
	return mb.ioModule
}

// GetAggregatorModule returns the aggregator module
func (mb *MessageBridgeImpl) GetAggregatorModule() AggregatorClientModule {
	return mb.aggregatorModule
}

// IsRunning returns whether the bridge is running
func (mb *MessageBridgeImpl) IsRunning() bool {
	return mb.isRunning
}
