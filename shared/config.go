package shared

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"
)

const (
	defaultQoS          = 2     // Default QoS level
	defaultPDSTime      = 5     // Default PDS time in seconds
	defaultLogTimestamp = false // Default log timestamp
)

// Global variables for configuration values
var (
	currentQoS     byte
	currentPDSTime time.Duration
	logTimestamp   bool
)

// Global variables for both MQTT clients
var serverConfig Config
var clientConfig Config
var mappingConfig MappingConfig

// MQTT topic variables - loaded from config files
var (
	// Server topics
	TopicArgusGetConfig           string
	TopicArgusReqControlData      string
	TopicArgusPullSensorData      string
	TopicArgusConfig              string
	TopicArgusPeriodic            string
	TopicArgusResponseControlData string

	// Client topics
	TopicFirmwareWriteDataReq        string
	TopicFirmwareWriteDataRes        string
	TopicFirmwareReadDataReq         string
	TopicFirmwareReadDataRes         string
	TopicFirmwareReadDataPeriodicRes string
)

// ConfigManager handles all configuration-related operations
type ConfigManager struct{}

// NewConfigManager creates a new configuration manager
func NewConfigManager() *ConfigManager {
	return &ConfigManager{}
}

// LoadConfigValues loads configuration values from environment variables
func (cm *ConfigManager) LoadConfigValues() {
	// Load QoS
	if qosStr := os.Getenv("MQTT_QOS"); qosStr != "" {
		if qos, err := strconv.Atoi(qosStr); err == nil && qos >= 0 && qos <= 2 {
			currentQoS = byte(qos)
			log.Printf("QoS level: %d", currentQoS)
		} else {
			currentQoS = defaultQoS
			log.Printf("Invalid QoS value, using default: %d", currentQoS)
		}
	} else {
		currentQoS = defaultQoS
		log.Printf("QoS level: %d", currentQoS)
	}

	// Load PDS Time
	if pdsTimeStr := os.Getenv("PDS_TIME"); pdsTimeStr != "" {
		if pdsTime, err := strconv.Atoi(pdsTimeStr); err == nil && pdsTime > 0 {
			currentPDSTime = time.Duration(pdsTime) * time.Second
			log.Printf("PDS time: %d seconds", pdsTime)
		} else {
			currentPDSTime = defaultPDSTime * time.Second
			log.Printf("Invalid PDS time value, using default: %d seconds", defaultPDSTime)
		}
	} else {
		currentPDSTime = defaultPDSTime * time.Second
		log.Printf("PDS time: %d seconds", defaultPDSTime)
	}

	// Load log timestamp
	if logTimestampStr := os.Getenv("LOG_TIMESTAMP"); logTimestampStr != "" {
		if logTimestampEnv, err := strconv.ParseBool(logTimestampStr); err == nil {
			logTimestamp = logTimestampEnv
		} else {
			logTimestamp = defaultLogTimestamp
		}
	} else {
		logTimestamp = defaultLogTimestamp
	}
}

// GetQoS returns the current QoS level
func GetQoS() byte {
	return currentQoS
}

// GetPDSTime returns the current PDS time
func GetPDSTime() time.Duration {
	return currentPDSTime
}

// GetLogTimestamp returns the current log timestamp
func GetLogTimestamp() bool {
	return logTimestamp
}

// GetIOConfigPath returns the appropriate IO config path based on environment
func GetIOConfigPath() string {
	// Check if we're running tests
	testingEnv := os.Getenv("TESTING")
	if testingEnv == "true" {
		return "test/test-firmware-config.json"
	}
	return "firmware-config.json"
}

// GetMappingConfigPath returns the appropriate mapping config path based on environment
func GetMappingConfigPath() string {
	// Check if we're running tests
	testingEnv := os.Getenv("TESTING")
	if testingEnv == "true" {
		return "test/test-mapping-config.json"
	}
	return "mapping-config.json"
}

// LoadMappingConfig loads the mapping configuration
func (cm *ConfigManager) LoadMappingConfig() error {
	configPath := GetMappingConfigPath()

	file, err := os.Open(configPath)
	if err != nil {
		return fmt.Errorf("failed to open mapping config file %s: %v", configPath, err)
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&mappingConfig); err != nil {
		return fmt.Errorf("failed to decode mapping config: %v", err)
	}

	log.Printf("✓ Loaded mapping config from %s", configPath)
	return nil
}

// LoadTopicConstants loads topic constants from configs
func (cm *ConfigManager) LoadTopicConstants() {
	// Load server topics
	if val, ok := serverConfig.SubscribeTopics["TopicArgusGetConfig"]; ok {
		TopicArgusGetConfig = val
	}
	if val, ok := serverConfig.SubscribeTopics["TopicArgusReqControlData"]; ok {
		TopicArgusReqControlData = val
	}
	if val, ok := serverConfig.SubscribeTopics["TopicArgusPullSensorData"]; ok {
		TopicArgusPullSensorData = val
	}
	if val, ok := serverConfig.PublishTopics["TopicArgusConfig"]; ok {
		TopicArgusConfig = val
	}
	if val, ok := serverConfig.PublishTopics["TopicArgusPeriodic"]; ok {
		TopicArgusPeriodic = val
	}
	if val, ok := serverConfig.PublishTopics["TopicArgusResponseControlData"]; ok {
		TopicArgusResponseControlData = val
	}

	// Load client topics
	if val, ok := clientConfig.SubscribeTopics["TopicFirmwareReadDataRes"]; ok {
		TopicFirmwareReadDataRes = val
	}
	if val, ok := clientConfig.SubscribeTopics["TopicFirmwareWriteDataRes"]; ok {
		TopicFirmwareWriteDataRes = val
	}
	if val, ok := clientConfig.SubscribeTopics["TopicFirmwareReadDataPeriodicRes"]; ok {
		TopicFirmwareReadDataPeriodicRes = val
	}
	if val, ok := clientConfig.PublishTopics["TopicFirmwareReadDataReq"]; ok {
		TopicFirmwareReadDataReq = val
	}
	if val, ok := clientConfig.PublishTopics["TopicFirmwareWriteDataReq"]; ok {
		TopicFirmwareWriteDataReq = val
	}
}

// LoadConfigs loads all configuration files
func (cm *ConfigManager) LoadConfigs() error {
	// Determine which config files to use
	serverConfigFile := "config-server.json"
	clientConfigFile := "config-client.json"

	// Check if we're running tests
	testingEnv := os.Getenv("TESTING")
	if testingEnv == "true" {
		serverConfigFile = "test/test-config-server.json"
		clientConfigFile = "test/test-config-client.json"
	}

	// Load server config
	if err := cm.loadConfigFile(serverConfigFile, &serverConfig); err != nil {
		return fmt.Errorf("failed to load server config: %v", err)
	}

	// Load client config
	if err := cm.loadConfigFile(clientConfigFile, &clientConfig); err != nil {
		return fmt.Errorf("failed to load client config: %v", err)
	}

	// Load mapping config
	if err := cm.LoadMappingConfig(); err != nil {
		return fmt.Errorf("failed to load mapping config: %v", err)
	}

	// Load topic constants from configs
	cm.LoadTopicConstants()

	return nil
}

// loadConfigFile is a helper function to load a single config file
func (cm *ConfigManager) loadConfigFile(filename string, config *Config) error {
	data, err := os.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("failed to read config file %s: %v", filename, err)
	}

	err = json.Unmarshal(data, config)
	if err != nil {
		return fmt.Errorf("failed to parse config JSON from %s: %v", filename, err)
	}

	return nil
}

// LoadIOFirmwareConfig loads IO firmware configuration
func LoadIOFirmwareConfig(filename string) (*IOFirmwareConfig, error) {
	// Check if file exists, if not throw error
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return nil, fmt.Errorf("IO Firmware config file not found at %s", filename)
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read IO firmware config: %v", err)
	}

	var ioConfig IOFirmwareConfig
	err = json.Unmarshal(data, &ioConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to parse IO firmware config: %v", err)
	}

	return &ioConfig, nil
}

// PrintConfigs prints the loaded configurations
func (cm *ConfigManager) PrintConfigs() {
	log.Printf("Loaded MQTT configurations:\n")
	for _, cfg := range []struct {
		name string
		cfg  Config
	}{
		{"Server", serverConfig},
		{"Client", clientConfig},
	} {
		log.Printf("%s Config:\n", cfg.name)
		log.Printf("  Broker: %s:%d\n", cfg.cfg.MQTT.Broker, cfg.cfg.MQTT.Port)
		log.Printf("  Username: %s\n", cfg.cfg.MQTT.Username)
		log.Printf("  Client ID: %s\n", cfg.cfg.MQTT.ClientID)
		log.Printf("  Subscribe topics: %d\n", len(cfg.cfg.SubscribeTopics))
		log.Printf("  Publish topics: %d\n", len(cfg.cfg.PublishTopics))
		log.Println()
	}
}

// GetServerConfig returns server configuration
func (cm *ConfigManager) GetServerConfig() Config {
	return serverConfig
}

// GetClientConfig returns client configuration
func (cm *ConfigManager) GetClientConfig() Config {
	return clientConfig
}

// GetMappingConfig returns mapping configuration
func (cm *ConfigManager) GetMappingConfig() MappingConfig {
	return mappingConfig
}

// GetIOConfig returns IO firmware configuration
func (cm *ConfigManager) GetIOConfig() (*IOFirmwareConfig, error) {
	return LoadIOFirmwareConfig(GetIOConfigPath())
}
