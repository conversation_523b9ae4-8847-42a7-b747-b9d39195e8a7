package shared

// DataChannel represents a communication channel between io and aggregatorClient
type DataChannel interface {
	// Send data from one module to another
	Send(data interface{}) error
	// Receive data from the channel
	Receive() (interface{}, error)
	// Close the channel
	Close() error
}

// IOToAggregatorChannel handles data flow from IO to AggregatorClient
type IOToAggregatorChannel interface {
	DataChannel
	// SendPeriodicData sends periodic sensor data from firmware to cloud
	SendPeriodicData(data CloudPeriodicData) error
	// SendWriteResponse sends write operation response from firmware to cloud
	SendWriteResponse(data CloudPeriodicData) error
	// SendReadResponse sends read operation response from firmware to cloud
	SendReadResponse(data CloudPeriodicData) error
}

// AggregatorToIOChannel handles data flow from AggregatorClient to IO
type AggregatorToIOChannel interface {
	DataChannel
	// SendReadRequest sends read request from cloud to firmware
	SendReadRequest(req interface{}) error
	// SendWriteRequest sends write request from cloud to firmware
	SendWriteRequest(req WriteRequest) error
}

// IOModule interface defines the responsibilities of the IO module
type IOModule interface {
	// Initialize the IO module with configuration
	Initialize(config Config) error

	// Start the IO module (connect to firmware, start listening)
	Start() error

	// Stop the IO module (disconnect, cleanup)
	Stop() error

	// SetAggregatorChannel sets the channel to communicate with aggregator
	SetAggregatorChannel(channel IOToAggregatorChannel)

	// GetIOChannel returns the channel for aggregator to send data to IO
	GetIOChannel() AggregatorToIOChannel

	// HandleFirmwareMessage processes messages from firmware
	HandleFirmwareMessage(topic string, payload []byte) error

	// SendToFirmware sends data to firmware
	SendToFirmware(topic string, data interface{}) error
}

// AggregatorClientModule interface defines the responsibilities of the AggregatorClient module
type AggregatorClientModule interface {
	// Initialize the aggregator client module with configuration
	Initialize(config Config) error

	// Start the aggregator client module (connect to cloud, start listening)
	Start() error

	// Stop the aggregator client module (disconnect, cleanup)
	Stop() error

	// SetIOChannel sets the channel to communicate with IO
	SetIOChannel(channel AggregatorToIOChannel)

	// GetAggregatorChannel returns the channel for IO to send data to aggregator
	GetAggregatorChannel() IOToAggregatorChannel

	// HandleCloudMessage processes messages from cloud
	HandleCloudMessage(topic string, payload []byte) error

	// SendToCloud sends data to cloud
	SendToCloud(topic string, data interface{}) error

	// PublishInitialConfig publishes initial configuration to cloud
	PublishInitialConfig() error
}

// MessageBridge facilitates communication between IO and AggregatorClient modules
type MessageBridge interface {
	// ConnectModules connects IO and AggregatorClient modules
	ConnectModules(io IOModule, aggregator AggregatorClientModule) error

	// Start starts the message bridge
	Start() error

	// Stop stops the message bridge
	Stop() error
}

// DataTransformer handles data transformation between different formats
type DataTransformer interface {
	// TransformPeriodicDataToCloud transforms firmware periodic data to cloud format
	TransformPeriodicDataToCloud(data PeriodicDataIO) CloudPeriodicData

	// TransformConfigToCloud transforms firmware config to cloud format
	TransformConfigToCloud(config *IOFirmwareConfig) *CloudPlatformConfig

	// FilterPeriodicData filters periodic data based on request
	FilterPeriodicData(data CloudPeriodicData, req ReadRequest) CloudPeriodicData
}

// ConfigurationManager handles configuration loading and management
type ConfigurationManager interface {
	// LoadConfigs loads all configuration files
	LoadConfigs() error

	// GetServerConfig returns server configuration
	GetServerConfig() Config

	// GetClientConfig returns client configuration
	GetClientConfig() Config

	// GetMappingConfig returns mapping configuration
	GetMappingConfig() MappingConfig

	// GetIOConfig returns IO firmware configuration
	GetIOConfig() (*IOFirmwareConfig, error)
}

// ValidationService handles validation of requests and data
type ValidationService interface {
	// ValidateControlRequest validates control requests
	ValidateControlRequest(req ControlRequest) error

	// ValidateReadRequest validates read requests
	ValidateReadRequest(req ReadRequest) error

	// ValidateWriteRequest validates write requests
	ValidateWriteRequest(req WriteRequest) error
}
