package shared

import "time"

// MQTT Configuration structures
type MQTTConfig struct {
	Broker   string `json:"broker"`
	Port     int    `json:"port"`
	Protocol string `json:"protocol"`
	Username string `json:"username"`
	Password string `json:"password"`
	ClientID string `json:"clientId"`
}

type Config struct {
	MQTT            MQTTConfig        `json:"mqtt"`
	SubscribeTopics map[string]string `json:"subscribeTopics"`
	PublishTopics   map[string]string `json:"publishTopics"`
}

type MappingConfig struct {
	CardZones   map[string]string `json:"cardZones"`
	ZoneIdNames map[string]string `json:"zoneIdNames"`
	ZoneIdHubs  map[string]string `json:"zoneIdHubs"`
}

// IO Firmware Configuration structures
type ValueConfig struct {
	Address string `json:"address"`
	Bytes   int    `json:"bytes"`
}

type RangeConfig struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

type Channel struct {
	ChannelID   string       `json:"channelId"`
	ChannelName string       `json:"channelName"`
	Type        string       `json:"type"`
	SignalType  string       `json:"signalType"`
	Value       ValueConfig  `json:"value"`
	Range       *RangeConfig `json:"range,omitempty"`
}

type Card struct {
	CardID     string    `json:"cardId"`
	ModbusAddr string    `json:"modbusAddr"`
	Channels   []Channel `json:"channels"`
}

type Port struct {
	BaudRate int    `json:"baudRate"`
	Parity   string `json:"parity"`
	Cards    []Card `json:"cards"`
}

type MQTTTopics struct {
	ReadReqTopic        string `json:"readReqTopic"`
	ReadResTopic        string `json:"readResTopic"`
	WriteReqTopic       string `json:"writeReqTopic"`
	WriteResTopic       string `json:"writeResTopic"`
	PeriodicResTopic    string `json:"periodicResTopic"`
	PeriodicityInterval int    `json:"periodicityInterval"`
}

type MQTTConf struct {
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type IOFirmwareConfig struct {
	ZoneName   string     `json:"zoneName"`
	DeviceID   string     `json:"deviceId"`
	MQTTConf   MQTTConf   `json:"mqttConf"`
	MQTTTopics MQTTTopics `json:"mqttTopics"`
	Ports      []Port     `json:"ports"`
}

// Cloud Platform Configuration structures
type CloudChannel struct {
	ChannelID   string       `json:"channelId"`
	ChannelName string       `json:"channelName"`
	Type        string       `json:"type"`
	SignalType  string       `json:"signalType"`
	Range       *RangeConfig `json:"range,omitempty"`
	Control     interface{}  `json:"control"`
}

type CloudCard struct {
	CardID   string         `json:"cardId"`
	CardName string         `json:"cardName,omitempty"`
	Channels []CloudChannel `json:"channels"`
}

type CloudZone struct {
	HubID    string      `json:"hubId,omitempty"`
	ZoneID   string      `json:"zoneId"`
	ZoneName string      `json:"zoneName,omitempty"`
	Cards    []CloudCard `json:"cards"`
}

type CloudPlatformConfig struct {
	UpdatedAt int64       `json:"updatedAt"`
	Zones     []CloudZone `json:"zones"`
}

// Request structures
type GetConfigRequest struct {
	RequestType string `json:"requestType"`
	Value       string `json:"value"`
}

type ReadRequest struct {
	RequestType string `json:"requestType"`
	Value       string `json:"value"`
}

type CardsReadRequest struct {
	RequestType string   `json:"requestType"`
	Values      []string `json:"values"`
}

// Periodic Data structures - IO Firmware format
type PeriodicChannel struct {
	ChannelID   string      `json:"channelId"`
	ChannelName string      `json:"channelName"`
	Value       interface{} `json:"value"`
	Unit        string      `json:"unit"`
	Status      string      `json:"status"`
}

type PeriodicCard struct {
	CardID   string            `json:"cardId"`
	CardName string            `json:"cardName,omitempty"`
	Channels []PeriodicChannel `json:"channels"`
}

type PeriodicDataIO struct {
	Timestamp int64          `json:"timestamp"`
	ZoneID    string         `json:"zoneId"`
	Cards     []PeriodicCard `json:"cards"`
}

// Periodic Data structures - Cloud Platform format
type CloudPeriodicZone struct {
	HubID    string         `json:"hubId,omitempty"`
	ZoneID   string         `json:"zoneId"`
	ZoneName string         `json:"zoneName,omitempty"`
	Cards    []PeriodicCard `json:"cards"`
}

type CloudPeriodicData struct {
	Timestamp int64               `json:"timestamp"`
	Zones     []CloudPeriodicZone `json:"zones"`
}

// Control Message structures
type ControlRequest struct {
	HubID       string      `json:"hubId"`
	ZoneID      string      `json:"zoneId"`
	CardID      string      `json:"cardId"`
	ChannelID   string      `json:"channelId"`
	ChannelName string      `json:"channelName"`
	Value       interface{} `json:"value"`
}

type ControlResponse struct {
	ZoneID    string `json:"zoneId"`
	CardID    string `json:"cardId"`
	ChannelID string `json:"channelId"`
	Status    string `json:"status"`
}

type WriteRequest struct {
	Cards []struct {
		CardID   string `json:"cardId"`
		Channels []struct {
			ChannelID   string      `json:"channelId"`
			ChannelName string      `json:"channelName"`
			Value       interface{} `json:"value"`
			Unit        string      `json:"unit"`
		} `json:"channels"`
	} `json:"cards"`
}

// PeriodicDataStore represents a store for periodic data with timestamp
type PeriodicDataStore struct {
	Data      CloudPeriodicData
	Timestamp time.Time
}
